@page "/checkout"
@* @rendermode InteractiveServer *@
@using Blazored.Toast.Services
@using Musewears.Client.Services.External
@using Musewears.Shared.Models
@using System
@using Musewears.Client.Services.InterswitchServices
@using Newtonsoft.Json;
@using PaymentRequest = Musewears.Shared.Models.PaymentRequest

@inject IJSRuntime JsRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject BasketState Basket
@inject OrderingService OrderingService

@* @inject IWardrobe Wardrobe *@
@* @inject IOrderService OrderService *@

@inject IToastService Toast

@inject InterSwitchWebCheckoutService WebCheckout

@if (_basketItems == null)
{
    <!-- Preloader, similar to the home page -->
    <div id="preloader">
        <div class="jumper">
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>
}
else
{

    <!-- check out section -->
    <div class="checkout-section mt-150 mb-150">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <div class="checkout-accordion-wrap">
                        <div class="accordion" id="accordionExample">
                            @* <div class="card single-accordion"> *@
                            @*     <div class="card-header" id="headingOne"> *@
                            @*         <h5 class="mb-0"> *@
                            @*             <button class="btn btn-link" type="button" data-toggle="collapse" *@
                            @*                 data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne"> *@
                            @*                 Billing Address *@
                            @*             </button> *@
                            @*         </h5> *@
                            @*     </div> *@
                            @* *@
                            @*     <div id="collapseOne" class="collapse show" aria-labelledby="headingOne" *@
                            @*         data-parent="#accordionExample"> *@
                            @*         <div class="card-body"> *@
                            @*             <div class="billing-address-form"> *@
                            @*                 <EditForm Model="@_billingAddress"> *@
                            @*                     <DataAnnotationsValidator /> *@
                            @*                     <p> *@
                            @*                         <label for="Name">Name:</label> *@
                            @*                         <InputText id="Name" @bind-Value="_billingAddress.Name" /> *@
                            @*                         <ValidationMessage For="@(() => _billingAddress.Name)" /> *@
                            @*                     </p> *@
                            @*                     &nbsp; *@
                            @*                     <p> *@
                            @*                         <label for="Email">Email:</label> *@
                            @*                         <InputText id="Email" @bind-Value="_billingAddress.Email" /> *@
                            @*                         <ValidationMessage For="@(() => _billingAddress.Email)" /> *@
                            @*                     </p> *@
                            @*                     &nbsp; *@
                            @*                     <p> *@
                            @*                         <label for="Phone">Phone:</label> *@
                            @*                         <InputText id="Phone" @bind-Value="_billingAddress.Phone" /> *@
                            @*                         <ValidationMessage For="@(() => _billingAddress.Phone)" /> *@
                            @*                     </p> *@
                            @*                     &nbsp; *@
                            @*                     <p> *@
                            @*                         <label for="City">City:</label> *@
                            @*                         <InputText id="City" @bind-Value="_billingAddress.City" /> *@
                            @*                         <ValidationMessage For="@(() => _billingAddress.City)" /> *@
                            @*                     </p> *@
                            @*                     &nbsp; *@
                            @*                     <p> *@
                            @*                         <label for="State">State:</label> *@
                            @*                         <InputText id="State" @bind-Value="_billingAddress.State" /> *@
                            @*                         <ValidationMessage For="@(() => _billingAddress.State)" /> *@
                            @*                     </p> *@
                            @*                     &nbsp; *@
                            @*                     <p> *@
                            @*                         <label for="Country">Country:</label> *@
                            @*                         <InputText id="Country" @bind-Value="_billingAddress.Country" /> *@
                            @*                         <ValidationMessage For="@(() => _billingAddress.Country)" /> *@
                            @*                     </p> *@
                            @*                     &nbsp; *@
                            @*                     <p> *@
                            @*                         <label for="Address">Address:</label> *@
                            @*                         <InputText id="Address" @bind-Value="_billingAddress.Address" /> *@
                            @*                         <ValidationMessage For="@(() => _billingAddress.Address)" /> *@
                            @*                     </p> *@
                            @*                     &nbsp; *@
                            @*                     <p> *@
                            @*                         <label for="ZipCode">ZipCode:</label> *@
                            @*                         <InputText id="ZipCode" @bind-Value="_billingAddress.ZipCode" /> *@
                            @*                         <ValidationMessage For="@(() => _billingAddress.ZipCode)" /> *@
                            @*                     </p> *@
                            @*                     $1$ <button type="submit">Save</button> #1# *@
                            @*                 </EditForm> *@
                            @*             </div> *@
                            @*         </div> *@
                            @*     </div> *@
                            @* </div> *@
                            <div class="card single-accordion">
                                <div class="card-header" id="headingTwo">
                                    <h5 class="mb-0">
                                        <button class="btn btn-link collapsed" type="button" data-toggle="collapse"
                                            data-target="#collapseTwo" aria-expanded="true" aria-controls="collapseTwo">
                                            Shipping Address
                                        </button>
                                    </h5>
                                </div>
                                <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                                    data-parent="#accordionExample">
                                    <div class="card-body">
                                        <div class="shipping-address-form">
                                            <div class="alert alert-info" style="background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 0.75rem; border-radius: 0.25rem; margin-bottom: 1rem;">
                                                <strong>Note:</strong> All fields marked with <span style="color: red;">*</span> are required.
                                            </div>
                                            <EditForm Model="@_shippingAddress" @ref="_shippingAddressForm">
                                                <DataAnnotationsValidator />
                                                <ValidationSummary />
                                                <p>
                                                    <label for="Name">Name: <span style="color: red;">*</span></label>
                                                    <InputText id="Name" @bind-Value="_shippingAddress.Name" class="form-control" required />
                                                    <ValidationMessage For="@(() => _shippingAddress.Name)" />
                                                </p>
                                                &nbsp;
                                                <p>
                                                    <label for="Email">Email: <span style="color: red;">*</span></label>
                                                    <InputText id="Email" @bind-Value="_shippingAddress.Email" class="form-control" required />
                                                    <ValidationMessage For="@(() => _shippingAddress.Email)" />
                                                </p>
                                                &nbsp;
                                                <p>
                                                    <label for="Phone">Phone: <span style="color: red;">*</span></label>
                                                    <InputText id="Phone" @bind-Value="_shippingAddress.Phone" class="form-control" required />
                                                    <ValidationMessage For="@(() => _shippingAddress.Phone)" />
                                                </p>
                                                &nbsp;
                                                <p>
                                                    <label for="City">City: <span style="color: red;">*</span></label>
                                                    <InputText id="City" @bind-Value="_shippingAddress.City" class="form-control" required />
                                                    <ValidationMessage For="@(() => _shippingAddress.City)" />
                                                </p>
                                                &nbsp;
                                                <p>
                                                    <label for="State">State: <span style="color: red;">*</span></label>
                                                    <InputText id="State" @bind-Value="_shippingAddress.State" class="form-control" required />
                                                    <ValidationMessage For="@(() => _shippingAddress.State)" />
                                                </p>
                                                &nbsp;
                                                <p>
                                                    <label for="Country">Country: <span style="color: red;">*</span></label>
                                                    <InputText id="Country" @bind-Value="_shippingAddress.Country" class="form-control" required />
                                                    <ValidationMessage For="@(() => _shippingAddress.Country)" />
                                                </p>
                                                &nbsp;
                                                <p>
                                                    <label for="Address">Address: <span style="color: red;">*</span></label>
                                                    <InputText id="Address" @bind-Value="_shippingAddress.Address" class="form-control" required />
                                                    <ValidationMessage For="@(() => _shippingAddress.Address)" />
                                                </p>
                                                &nbsp;
                                                <p>
                                                    <label for="ZipCode">ZipCode: <span style="color: red;">*</span></label>
                                                    <InputText id="ZipCode" @bind-Value="_shippingAddress.ZipCode" class="form-control" required />
                                                    <ValidationMessage For="@(() => _shippingAddress.ZipCode)" />
                                                </p>

                                                @* <button type="submit">Save</button> *@
                                            </EditForm>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="order-details-wrap">
                        <table class="order-details">
                            <thead>
                                <tr>
                                    <th>Your order Details</th>
                                    <th>Price </th>
                                </tr>
                            </thead>
                            <tbody class="order-details-body">

                                <tr>
                                    <td>Product</td>
                                    <td>Total</td>
                                </tr>

                                @foreach (var cart in _basketItems)
                                {
                                    <tr>
                                        <td>@cart.ProductName</td>
                                        <td>₦@cart.UnitPrice.ToString("N2")</td>
                                    </tr>
                                }
                            </tbody>
                            <tbody class="checkout-details">
                                <tr>
                                    <td>Subtotal</td>
                                    <td>₦@Subtotal.ToString("N2")</td>
                                </tr>
                                <tr>
                                    <td>Shipping</td>
                                    <td>₦@(ShippingFee.ToString("N2"))</td>
                                </tr>
                                <tr>
                                    <td>Total</td>
                                    <td>₦@Total.ToString("N2")</td>
                                </tr>
                            </tbody>
                        </table>
                        <a class="boxed-btn @(IsFormValid() && !_isPlacingOrder ? "" : "disabled")"
                           @onclick=@(async () => await PlaceOrder())
                           style="@(IsFormValid() && !_isPlacingOrder ? "" : "opacity: 0.6; cursor: not-allowed;")">
                            @if (_isPlacingOrder)
                            {
                                <span>Processing...</span>
                            }
                            else
                            {
                                <span>Place Order</span>
                            }
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- end check out section -->
    

    <script>
      // grab it out of the hidden input that Html.AntiForgeryToken() generated
      window.CSRF_TOKEN = document.querySelector('input[name="__RequestVerificationToken"]').value;
    </script>



    <BlazoredToasts Position="ToastPosition.BottomRight" Timeout="5" ShowProgressBar="false" />

}

<style>
    .validation-message {
        color: #dc3545;
        font-size: 0.875em;
        margin-top: 0.25rem;
    }

    .validation-summary {
        color: #dc3545;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .form-control.invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .required-field {
        color: #dc3545;
        font-weight: bold;
    }

    .shipping-address-form p {
        margin-bottom: 1rem;
    }

    .shipping-address-form label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .shipping-address-form .form-control {
        width: 100%;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .shipping-address-form .form-control:focus {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
</style>

@code {

    private IReadOnlyCollection<BasketItem>? _basketItems;

    private static decimal ShippingFee => 10000;

    private decimal Subtotal => _basketItems?.Sum(x => x.UnitPrice * x.Quantity) ?? 0;

    private decimal Total => ShippingFee + Subtotal;

    private ShippingAddress _shippingAddress = new ShippingAddress();

    // private BillingAddress _billingAddress = new BillingAddress();

    private BasketCheckoutInfo? _checkoutInfo;

    private EditForm? _shippingAddressForm;
    
    IDisposable? _basketStateSubscription;

    IDisposable? _paymentStateSubscription;

    private bool _isPlacingOrder = false;
    private bool _isProcessingPayment = false;

    protected override async Task OnInitializedAsync()
    {
        // The basket contents may change during the lifetime of this component (e.g., when an item is
        // added during the current request). If this EventCallback is invoked, it will cause this
        // component to re-render with the updated data.
        _basketStateSubscription = Basket.NotifyOnChange(
            EventCallback.Factory.Create(this, UpdateBasketItemsAsync));

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
        if (userId != null)
        {
            // Initialize shipping address with user ID
            _shippingAddress.UserId = userId;
            _shippingAddress.Id = Guid.NewGuid().ToString();

            await UpdateBasketItemsAsync();
            @* StateHasChanged(); *@
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _paymentStateSubscription = await WebCheckout.NotifyOnChange(EventCallback.Factory.Create<PaymentResponse>(this, UpdateOrder));
            // Pass a DotNetObjectReference to this component instance to JavaScript
            // await JsRuntime.InvokeVoidAsync("setPaymentHandler", DotNetObjectReference.Create(this));
        }
    }
    
    private async Task UpdateBasketItemsAsync()
    {
        _basketItems = await Basket.GetBasketItemsAsync();
    }

    private async Task PlaceOrder()
    {
        // Prevent multiple submissions
        if (_isPlacingOrder) return;

        _isPlacingOrder = true;
        StateHasChanged();

        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (userId != null)
            {
            // Validate shipping address form before proceeding
            if (_shippingAddressForm?.EditContext?.Validate() != true)
            {
                Toast.ShowError("Please fill in all required shipping address fields correctly.");
                return;
            }

            // Check if all required fields are filled
            if (string.IsNullOrWhiteSpace(_shippingAddress.Name) ||
                string.IsNullOrWhiteSpace(_shippingAddress.Email) ||
                string.IsNullOrWhiteSpace(_shippingAddress.Phone) ||
                string.IsNullOrWhiteSpace(_shippingAddress.City) ||
                string.IsNullOrWhiteSpace(_shippingAddress.State) ||
                string.IsNullOrWhiteSpace(_shippingAddress.Country) ||
                string.IsNullOrWhiteSpace(_shippingAddress.Address) ||
                string.IsNullOrWhiteSpace(_shippingAddress.ZipCode))
            {
                Toast.ShowError("All shipping address fields are required. Please fill in all fields.");
                return;
            }

            try
            {
                // Generate payment reference (txn_ref) first
                var paymentReference = Guid.NewGuid().ToString();

                _checkoutInfo = new BasketCheckoutInfo
                {
                    Buyer = userId,
                    Street = _shippingAddress.Address,
                    City = _shippingAddress.City,
                    State = _shippingAddress.State,
                    ZipCode = _shippingAddress.ZipCode,
                    Country = _shippingAddress.Country,
                    RequestId = Guid.Parse(paymentReference) // Use payment reference as request ID for idempotency
                };

                // Create order first, then initiate payment
                await Basket.CheckoutAsync(_checkoutInfo, paymentReference);
                await MakePayment(paymentReference);

                // Note: Basket will be cleared in UpdateOrder callback only for successful payments
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                Toast.ShowError(ex.Message);
            }
        }
        else
        {
            Toast.ShowError("Please login to proceed");
        }
        }
        finally
        {
            _isPlacingOrder = false;
            StateHasChanged();
        }
    }

    private async Task MakePayment(string paymentReference)
    {

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
        var userName = authState.User.FindFirst(System.Security.Claims.ClaimTypes.Name)?.Value;
        var userEmail = authState.User.FindFirst(System.Security.Claims.ClaimTypes.Email)?.Value;

        foreach (var claim in authState.User.Claims)
        {
            Console.WriteLine($"Claim Type: {claim.Type}, Value: {claim.Value}");
        }

        if (_basketItems == null)
        {

            Toast.ShowWarning("You have no items in your cart");
            return;
        }

        if (userId == null)
        {

            Toast.ShowWarning("Please login to proceed");
            return;
        }

        var paymentRequest = new PaymentRequest
            {
                merchant_code = "MX190087",
                pay_item_id = "6101231",
                pay_item_name = "Musewears Purchase",
                secret = "Vv8Iualc3pdLJvs",
                client_id = "IKIABC91B6BE748A4A567214B77E729445190D83A6E2",
                txn_ref = paymentReference, // Use the same reference as the order
                site_redirect_url = "https://google.com/",
                amount = Total * 100,
                currency = (int)CurrencyCode.NGN, // ISO 4217 numeric code of the currency used
                mode = "TEST",
                // Customer information for Interswitch webhook
                cust_id = userId, // Use email as customer ID (more meaningful than GUID)
                cust_name = userName,
                // cust_email = userEmail,
                // cust_mobile_no = ""
            };

        await WebCheckout.InitiateInlineCheckout(paymentRequest, userId);

        // await JsRuntime.InvokeVoidAsync("initiatePayment", paymentRequest, userId);
    }

    // This method must be static and be decorated with [JSInvokable]
    // [JSInvokable]
    private async Task UpdateOrder(PaymentResponse? response)
    {
        Console.WriteLine($"Payment Response: {response?.resp}");

        // Handle the payment response here
        if (response == null)
        {
            Toast.ShowError("Payment response was not received. Please try again.");
            return;
        }

        // Prevent multiple processing of the same payment response
        if (_isProcessingPayment)
        {
            Console.WriteLine("Payment is already being processed, ignoring duplicate callback");
            return;
        }

        _isProcessingPayment = true;

        var txnref = response.txnref;
        var paymentReference = response.payRef;
        var isSuccessfulPayment = response.resp == PaymentResponseCode.Approved ||
                                  response.resp == PaymentResponseCode.ApprovedPartial ||
                                  response.resp == PaymentResponseCode.ApprovedVIP ||
                                  response.resp == PaymentResponseCode.InProgress;

        if (isSuccessfulPayment)
        {
            // Convert response code to PaymentStatus
            var paymentStatus = PaymentResponseCode.GetPaymentStatus(response.resp);

            try
            {
                // Update order status as fallback in case webhook isn't called
                if (!string.IsNullOrEmpty(txnref))
                {
                    var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
                    var userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                    var userName = authState.User.FindFirst(System.Security.Claims.ClaimTypes.Name)?.Value;

                    // Use txnref as MerchantReference to find the order, and payRef as PaymentReference
                    await OrderingService.UpdatePaymentStatusAsync(response.payRef, txnref, paymentStatus.ToString(), userId, userName, response.resp);
                }

                // Clear basket only for successful payments (pending or completed)
                await Basket.DeleteBasketAsync();

                // Show appropriate success messages
                if (response.resp == PaymentResponseCode.Approved ||
                    response.resp == PaymentResponseCode.ApprovedPartial ||
                    response.resp == PaymentResponseCode.ApprovedVIP)
                {
                    Toast.ShowSuccess("Payment completed successfully! Your order has been placed.");
                }
                else if (response.resp == PaymentResponseCode.InProgress)
                {
                    Toast.ShowInfo("Payment is being processed. Please wait for confirmation.");
                }
            }
            catch (Exception ex)
            {
                Toast.ShowError($"Error processing payment: {ex.Message}");
                Console.WriteLine($"Error in UpdateOrder: {ex.Message}");
                _isProcessingPayment = false; // Reset flag on error
            }
        }
        else
        {
            // Payment failed or was cancelled - keep basket so user can retry
            Toast.ShowError("Payment was not successful. Please try again.");
            Console.WriteLine($"Payment failed with response code: {response.resp}");
        }

        // Reset the processing flag
        _isProcessingPayment = false;
        return;

            // Deserialize the payment response if necessary
        @* var response = JsonSerializer.Deserialize<PaymentResponse>(paymentResponse.ToString()); *@

            // Implement the logic to update the order based on the response
        @* await OrderService.UpdateOrderStatus(response.OrderId, "Paid"); // Replace with actual order update logic *@
    }



    private bool IsFormValid()
    {
        return !string.IsNullOrWhiteSpace(_shippingAddress.Name) &&
               !string.IsNullOrWhiteSpace(_shippingAddress.Email) &&
               !string.IsNullOrWhiteSpace(_shippingAddress.Phone) &&
               !string.IsNullOrWhiteSpace(_shippingAddress.City) &&
               !string.IsNullOrWhiteSpace(_shippingAddress.State) &&
               !string.IsNullOrWhiteSpace(_shippingAddress.Country) &&
               !string.IsNullOrWhiteSpace(_shippingAddress.Address) &&
               !string.IsNullOrWhiteSpace(_shippingAddress.ZipCode) &&
               (_shippingAddressForm?.EditContext?.GetValidationMessages().Any() != true);
    }

    public void Dispose()
    {
        _basketStateSubscription?.Dispose();
        _paymentStateSubscription?.Dispose();
    }
}