using System.Net.Http.Headers;
using Blazored.LocalStorage;
using Blazored.Toast;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Authentication;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using MudBlazor.Services;
using Musewears.Client;
using Musewears.Client.Api;
using Musewears.Client.Extensions;
using Musewears.Client.Services;
using Musewears.Client.Services.InterswitchServices;
using Refit;
using WebAppComponents.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

//builder.RootComponents.Add<App>("#main-wrapper");
//builder.RootComponents.Add<HeadOutlet>("head::after");

//builder.Services.AddHttpClient("Musewears.ServerAPI", client => client.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress))
//    .AddHttpMessageHandler<BaseAddressAuthorizationMessageHandler>();

// builder.Services.AddHttpClient("default", client =>
// {
//     client.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress);
//     // client.BaseAddress = new Uri("http://identity-api/");
//     // client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
// }).AddApiVersion(2.0)
// .AddHttpMessageHandler<BaseAddressAuthorizationMessageHandler>();

builder.Services.AddAuthorizationCore();
builder.Services.AddCascadingAuthenticationState();
builder.Services.AddAuthenticationStateDeserialization();
// builder.Services.AddSingleton<AuthenticationStateProvider, PersistentAuthenticationStateProvider>();

builder.AddApplicationServices();

builder.Services.AddBlazoredToast();

builder.Services.AddMudServices();

builder.Services.AddScoped<UserService>();

builder.Services.AddSingleton<MusewearDialogService>();

// register your Interswitch checkout service
builder.Services.AddScoped<InterSwitchWebCheckoutService>();

// builder.Services.AddOidcAuthentication(options =>
// {
//     // Configure your authentication provider options here.
//     // For more information, see https://aka.ms/blazor-standalone-auth
//     builder.Configuration.Bind("Local", options.ProviderOptions);
// });

// Supply HttpClient instances that include access tokens when making requests to the server project
// builder.Services.AddScoped(sp => sp.GetRequiredService<IHttpClientFactory>().CreateClient("Musewears.ServerAPI"));

builder.Services.AddApiAuthorization();

var settings = new RefitSettings(new SystemTextJsonContentSerializer());

// Register Refit service
builder.Services.AddRefitClient<IWearApi>(settings)
    .ConfigureHttpClient(c => c.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress))
    .AddApiVersion(2.0) 
    // .AddHttpMessageHandler<BaseAddressAuthorizationMessageHandler>()
    ;

builder.Services.AddRefitClient<IAccountApi>(settings)
    .ConfigureHttpClient(c => c.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress))
    .AddApiVersion(2.0)
    // .AddHttpMessageHandler<CustomTokenHandler>()
    // .AddHttpMessageHandler<BaseAddressAuthorizationMessageHandler>()
    ;

// Console.WriteLine($"Running on {builder.HostEnvironment.BaseAddress}"); 

var host = builder.Build();

await host.RunAsync();

