var builder = DistributedApplication.CreateBuilder(args);


// Add Redis
var redis = builder.AddRedis("redis")
    .WithRedisInsight() // Optional: Adds Redis Insight for debugging
    .PublishAsContainer()
    .WithDataVolume();

var postgres = builder.AddPostgres("postgres")
        .WithImage("ankane/pgvector")
        .WithImageTag("latest")
        .WithDataVolume()
        .WithPgAdmin()
    // .WithLifetime(ContainerLifetime.Persistent)
    ;

// Add PostgreSQL databases
var musewearsDb = postgres.AddDatabase("musewears");

var catalogDb = postgres.AddDatabase("catalog");

var orderingDb = postgres.AddDatabase("ordering");

// Add your main server project
var server = builder.AddProject<Projects.Musewears_Server>("web")
    .WithReference(redis)
    .WithReference(musewearsDb)
    .WithReference(catalogDb)
    .WithReference(orderingDb)
    .WaitFor(redis)
    .WaitFor(musewearsDb)
    .WaitFor(catalogDb)
    .WaitFor(orderingDb)
    .WithEnvironment("ASPNETCORE_ENVIRONMENT", builder.Configuration["ASPNETCORE_ENVIRONMENT"] ?? "Development")
    .WithEnvironment("INTERSWITCH_SECRET_KEY", builder.Configuration["INTERSWITCH_SECRET_KEY"] ?? "");

// Optional: Add health checks dashboard
// builder.AddProject<Projects.Musewears_Server>("server")
//     .WithEnvironment("ASPNETCORE_ENVIRONMENT", builder.Configuration["ASPNETCORE_ENVIRONMENT"] ?? "Development")
//     .WithEnvironment("INTERSWITCH_SECRET_KEY", builder.Configuration["INTERSWITCH_SECRET_KEY"] ?? "")
//     .WithHealthCheck("/health");

builder.Build().Run();