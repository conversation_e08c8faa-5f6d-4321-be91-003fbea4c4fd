using System.ComponentModel.DataAnnotations;

namespace Musewears.Server.Models;

/// <summary>
/// View model for user list display
/// </summary>
public class UserViewModel
{
    public string Id { get; set; } = string.Empty;
    public string? Email { get; set; }
    public string? UserName { get; set; }
    public string? Fullname { get; set; }
    public bool EmailConfirmed { get; set; }
    public bool LockoutEnabled { get; set; }
    public DateTimeOffset? LockoutEnd { get; set; }
    public DateTime? LastLoginDate { get; set; }
    public List<string> Roles { get; set; } = new();
    public bool IsLockedOut => LockoutEnd.HasValue && LockoutEnd > DateTimeOffset.UtcNow;
}

/// <summary>
/// Detailed view model for single user display
/// </summary>
public class UserDetailViewModel
{
    public string Id { get; set; } = string.Empty;
    public string? Email { get; set; }
    public string? UserName { get; set; }
    public string? Fullname { get; set; }
    public string? PhoneNumber { get; set; }
    public bool EmailConfirmed { get; set; }
    public bool PhoneNumberConfirmed { get; set; }
    public bool TwoFactorEnabled { get; set; }
    public bool LockoutEnabled { get; set; }
    public DateTimeOffset? LockoutEnd { get; set; }
    public int AccessFailedCount { get; set; }
    public DateTime? LastLoginDate { get; set; }
    public DateTime CreatedDate { get; set; }
    public List<string> Roles { get; set; } = new();
    public bool IsLockedOut => LockoutEnd.HasValue && LockoutEnd > DateTimeOffset.UtcNow;
}

/// <summary>
/// Request model for assigning roles to a user
/// </summary>
public class AssignRolesRequest
{
    [Required]
    [MinLength(1, ErrorMessage = "At least one role must be assigned")]
    public List<string> Roles { get; set; } = new();
}

/// <summary>
/// Request model for creating a new user
/// </summary>
public class CreateUserRequest
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [Required]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be at least 6 characters long")]
    public string Password { get; set; } = string.Empty;

    [Required]
    [StringLength(100, MinimumLength = 2)]
    public string Fullname { get; set; } = string.Empty;

    [Phone]
    public string? PhoneNumber { get; set; }

    public List<string> Roles { get; set; } = new();
    public bool EmailConfirmed { get; set; } = false;
    public bool RequirePasswordChange { get; set; } = true;
}

/// <summary>
/// Request model for updating user information
/// </summary>
public class UpdateUserRequest
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [Required]
    [StringLength(100, MinimumLength = 2)]
    public string Fullname { get; set; } = string.Empty;

    [Phone]
    public string? PhoneNumber { get; set; }

    public bool EmailConfirmed { get; set; }
    public bool PhoneNumberConfirmed { get; set; }
    public bool TwoFactorEnabled { get; set; }
    public bool LockoutEnabled { get; set; }
}

/// <summary>
/// Request model for user lockout operations
/// </summary>
public class LockoutUserRequest
{
    [Required]
    public DateTime LockoutEnd { get; set; }

    [StringLength(500)]
    public string? Reason { get; set; }
}

/// <summary>
/// Request model for password reset
/// </summary>
public class ResetPasswordRequest
{
    [Required]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be at least 6 characters long")]
    public string NewPassword { get; set; } = string.Empty;

    public bool RequirePasswordChange { get; set; } = true;
}

/// <summary>
/// Response model for user operations
/// </summary>
public class UserOperationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Model for available roles
/// </summary>
public class RoleViewModel
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public int UserCount { get; set; }
}
