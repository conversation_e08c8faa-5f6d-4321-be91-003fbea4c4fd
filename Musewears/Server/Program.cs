using Basket.API.Extensions;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Builder.Extensions;
using Microsoft.AspNetCore.Builder;
using Musewears.Server;
using Musewears.Server.Components;
using Musewears.Server.Components.Account;
using Musewears.Server.Data;
// using Musewears.Client.Pages;
using Microsoft.AspNetCore.Routing;
using Musewears.Client;
using Musewears.Shared.Models;
using Musewears.Server.Services;
using Musewears.Server.Services.SquareServices;
using Musewears.Server.Middleware;
using Blazored.Toast;
using Catalog.API.Extensions;
using Microsoft.AspNetCore.Mvc;
using Musewears.Client.Services;
using Musewears.Server.Models;
using Npgsql;
using ServiceDefaults;
using WebAppComponents.Services;
using Yarp.ReverseProxy.Forwarder;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Ordering.API.Extensions;

// using Musewears.Server.Pages;

var builder = WebApplication.CreateBuilder(args);

//builder.WebHost.UseUrls("http://0.0.0.0:" + Environment.GetEnvironmentVariable("PORT"));

// Check the environment and set the connection string accordingly
//if (builder.Environment.IsProduction())
//{
//    connectionString = builder.Configuration.GetConnectionString("ProductionConnection");
//}
//else
//{
//    connectionString = builder.Configuration.GetConnectionString("DevelopmentConnection");
//}

// Add services to the container.
//var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");


// Only use Aspire in Development when AppHost is running
if (builder.Environment.IsDevelopment())
{
    // Aspire configuration (local development)
    builder.AddServiceDefaults();
    builder.AddNpgsqlDbContext<ApplicationDbContext>("musewears");
}
else
{
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
    string npgsqlConn;

    if (connectionString!.StartsWith("postgresql://", StringComparison.OrdinalIgnoreCase))
    {
        var uri = new Uri(connectionString);
        var parts = uri.UserInfo.Split(':', 2);
        var bldr = new NpgsqlConnectionStringBuilder
        {
            Host                     = uri.Host,
            // Port                     = uri.Port,
            Database                 = uri.AbsolutePath.TrimStart('/'),
            Username                 = parts[0],
            Password                 = parts.Length > 1 ? parts[1] : "",
            SslMode                  = SslMode.Require                   // or false, per your policy
        };
        npgsqlConn = bldr.ToString();
    }
    else
    {
        npgsqlConn = connectionString;
    }
    
    builder.Services.AddDbContext<ApplicationDbContext>(options =>
        options.UseNpgsql(npgsqlConn));
}


// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents()
    .AddAuthenticationStateSerialization(options =>
    {
        options.SerializeAllClaims = true;
    });

builder.Services.AddCascadingAuthenticationState();
builder.Services.AddDatabaseDeveloperPageExceptionFilter();
builder.Services.AddScoped<IdentityUserAccessor>();
builder.Services.AddScoped<IdentityRedirectManager>();
builder.Services.AddIdentityCore<ApplicationUser>(options => options.SignIn.RequireConfirmedAccount = true)
        .AddRoles<IdentityRole>()
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddSignInManager()
    .AddDefaultTokenProviders();

builder.Services.AddServerSideBlazor();
builder.Services.AddScoped<AuthenticationStateProvider, RevalidatingIdentityAuthenticationStateProvider<ApplicationUser>>();

builder.Services.AddSingleton<MusewearDialogService>();

// builder.Services.AddScoped<Radzen.DialogService>();
// builder.Services.AddScoped<Radzen.NotificationService>();
// builder.Services.AddScoped<Radzen.TooltipService>();
// builder.Services.AddScoped<Radzen.ContextMenuService>();

builder.Services.AddAuthentication(options =>
{
    // use the cookie‐handler for everything except JwtBearer
    options.DefaultAuthenticateScheme = IdentityConstants.ApplicationScheme;
    options.DefaultSignInScheme     = IdentityConstants.ApplicationScheme;
    options.DefaultChallengeScheme  = IdentityConstants.ApplicationScheme;

    // options.DefaultScheme = IdentityConstants.ApplicationScheme;
    // options.DefaultAuthenticateScheme = IdentityConstants.ApplicationScheme;
    // options.DefaultChallengeScheme = IdentityConstants.ApplicationScheme;
    // Do NOT set anything to IdentityConstants.BearerScheme here
})
.AddBearerToken(IdentityConstants.BearerScheme)
.AddIdentityCookies() // This must be last!
;

builder.Services.Configure<IdentityOptions>(options =>
{
    // Configure password requirements
    options.Password.RequireDigit = true;
    options.Password.RequiredLength = 8;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireLowercase = true;
    
    // Configure lockout settings
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;
});

builder.Services.AddSingleton<IEmailSender, NoOpEmailSender>();

builder.Services.AddSingleton<IEmailSender<ApplicationUser>, IdentityNoOpEmailSender>();

builder.Services.AddTransient<ISqlDataAccess, SqlDataAccess>();
builder.Services.AddTransient<IWardrobe, Wardrobe>();
// builder.Services.AddScoped<IOrderService, OrderService>();
builder.Services.AddScoped<ICartService, CartService>();

// Add MediatR for domain events and commands integration with Ordering project
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(Ordering.API.Application.Commands.ProcessInterswitchPaymentCommand).Assembly));

builder.Services.AddScoped<IOrderingIntegrationService, OrderingIntegrationService>();

// Add webhook metrics service
builder.Services.AddSingleton<IWebhookMetricsService, WebhookMetricsService>();

builder.Services.AddScoped(sp => sp.GetRequiredService<IHttpClientFactory>().CreateClient("ServerAPI"));
builder.Services.AddApiAuthorization();

//builder.Services.AddRefitClient<IAccountApi>()
//    .ConfigureHttpClient((sp, c) =>
//    {
//        var navigationManager = sp.GetRequiredService<HttpClient>();
//        c.BaseAddress = navigationManager.BaseAddress;
//    });
//.AddHttpMessageHandler<BaseAddressAuthorizationMessageHandler>();



//builder.Services.AddRefitClient<IAccountApi>()
//    .ConfigureHttpClient(c => c.BaseAddress = new Uri("https://localhost:7135/"));

//builder.Services.AddDefaultIdentity<ApplicationUser>(options => options.SignIn.RequireConfirmedAccount = true)
//    .AddEntityFrameworkStores<ApplicationDbContext>();

// builder.Services.AddAuthorization();


// builder.Services.AddIdentityApiEndpoints<IdentityUser>().AddEntityFrameworkStores<ApplicationDbContext>();

// builder.Services.AddIdentity<IdentityUser, IdentityRole>(options =>
// {
//    options.SignIn.RequireConfirmedEmail = false;
// })
//    .AddEntityFrameworkStores<ApplicationDbContext>();

// builder.Services.AddIdentityServer();

// builder.Services.AddAuthentication()
//    .AddIdentityServerJwt();

if (builder.Environment.IsDevelopment())
{
    builder.WebHost.UseStaticWebAssets();
}

builder.Services.AddControllersWithViews();
builder.Services.AddRazorPages();

builder.Services.AddCors(options =>
{
    options.AddPolicy("CorsPolicy",
        policyBuilder => policyBuilder
            .AllowAnyMethod()
            .AllowCredentials()
            .SetIsOriginAllowed((host) => true)
            .AllowAnyHeader());
});

builder.Services.AddEndpointsApiExplorer();

// builder.Services.AddSwaggerGen();

builder.Services.AddBlazoredToast();

// builder.Services.AddMudServices();

builder.Services.AddHttpContextAccessor();

// Square API setup
var squareData = builder.Configuration.GetSection("Square").Get<SquareData>()!;
builder.Services.AddSingleton(squareData);
builder.Services.AddTransient<SquareHelper>();

builder.AddCatalogServices();
builder.AddBasketServices();
builder.AddOrderingServices();

builder.Services.AddHttpForwarder();

builder.Services.AddAntiforgery(options =>
{
  // options.HeaderName = "X-CSRF-TOKEN";
});


var withApiVersioning = builder.Services.AddApiVersioning();

builder.AddDefaultOpenApi(withApiVersioning);

var app = builder.Build();

// app.UseSwaggerUI();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseMigrationsEndPoint();
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

// app.UseSwagger();

// // Check if the database exists
using var scope = app.Services.CreateScope();
var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
var databaseExists = dbContext.Database.CanConnect();

var pendingMigrations = dbContext.Database.GetPendingMigrations();

if (pendingMigrations.Any())
{
    // If the database doesn't exist, create it
    dbContext.Database.Migrate();
    // Optionally, you can add data seeding logic here
}
else
{
    dbContext.Database.EnsureCreated();
}

// Seed roles and users after building the app
var services = scope.ServiceProvider;
    
try
{
    var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();
    var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
        
    await IdentitySeeder.SeedRolesAsync(roleManager);
    await IdentitySeeder.SeedAdminUserAsync(userManager, roleManager);
}
catch (Exception ex)
{
    var logger = services.GetRequiredService<ILogger<Program>>();
    logger.LogError(ex, "An error occurred while seeding the database.");
}

app.MapIdentityApi<ApplicationUser>();


// app.MapReverseProxy();

app.UseHttpsRedirection();

// app.UseBlazorFrameworkFiles();
app.MapStaticAssets();

app.UseRouting();

// Add webhook correlation middleware before authentication
app.UseMiddleware<WebhookCorrelationMiddleware>();

app.UseAntiforgery();

//app.UseIdentityServer();
app.UseAuthentication();
app.UseAuthorization();

//app.MapBlazorHub();
// app.MapFallbackToPage("/_Host");

app.MapRazorPages();
app.MapControllers();
// app.MapFallbackToFile("index.html");

app.UseCors("CorsPolicy");

app.UseCatalogApi();
app.UseBasketApi();
app.UseOrderingApi();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode()
    .AddAdditionalAssemblies(typeof(Musewears.Client._Imports).Assembly)
    ;

// Add additional endpoints required by the Identity /Account Razor components.
app.MapAdditionalIdentityEndpoints();

var forwarder   = app.Services.GetRequiredService<IHttpForwarder>();

app.MapGet("/product-images/{id}", async context =>
{
    // build the exact full URI you want to call
    var id   = context.Request.RouteValues["id"]?.ToString();
    var qs   = context.Request.QueryString; // preserves ?api-version=2.0 etc.
    var dest = new UriBuilder
    {
        Scheme = context.Request.Scheme,
        Host   = context.Request.Host.Host,
        Port   = context.Request.Host.Port ?? (context.Request.Scheme=="https"?443:80),
        Path   = $"/api/catalog/items/{id}/pic",
        Query  = qs.Value?.TrimStart('?') ?? ""
    }.Uri;

    // 1) turn Uri into the "destination prefix" string (scheme+host+port)
    var prefix = dest.GetLeftPart(UriPartial.Authority);

    // 2) use a message invoker for the outgoing call
    using var httpClient = new HttpMessageInvoker(new HttpClientHandler());

    // 3) pick the overload you need:
    //    here we don’t supply a transformer, so it will copy path+query automatically
    var error = await forwarder.SendAsync(
        context,
        prefix,
        httpClient,
        ForwarderRequestConfig.Empty
    );

    if (error != ForwarderError.None)
        context.Response.StatusCode = StatusCodes.Status502BadGateway;
});


app.UseDefaultOpenApi();

// app.MapForwarder("/product-images/{id}", "https://localhost:7135", "/api/catalog/items/{id}/pic");

app.Run();

// Seed admin roles
static async Task SeedRolesAsync(IServiceProvider serviceProvider)
{
    var roleManager = serviceProvider.GetRequiredService<RoleManager<IdentityRole>>();
    var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();

    string[] roleNames = { "Admin", "Manager", "User" };
    
    foreach (var roleName in roleNames)
    {
        var roleExist = await roleManager.RoleExistsAsync(roleName);
        if (!roleExist)
        {
            await roleManager.CreateAsync(new IdentityRole(roleName));
        }
    }
}