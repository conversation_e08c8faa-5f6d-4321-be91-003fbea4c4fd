using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.Events;
using OrderingApiTrace = Ordering.API.Extensions.OrderingApiTrace;

namespace Ordering.API.Application.DomainEventHandlers;

public class InterswitchPaymentProcessedDomainEventHandler(
    ILogger<InterswitchPaymentProcessedDomainEventHandler> logger,
    IBuyerRepository buyerRepository)
    : INotificationHandler<InterswitchPaymentProcessedDomainEvent>
{
    private readonly ILogger _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    private readonly IBuyerRepository _buyerRepository = buyerRepository ?? throw new ArgumentNullException(nameof(buyerRepository));

    public async Task Handle(InterswitchPaymentProcessedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling Interswitch payment processed domain event for payment reference: {PaymentReference}",
            domainEvent.PaymentReference);

        // Use the merchant customer info if available, otherwise fall back to user info
        var buyerUserId = !string.IsNullOrEmpty(domainEvent.MerchantCustomerId)
            ? domainEvent.MerchantCustomerId
            : domainEvent.UserId;
        var buyerUserName = !string.IsNullOrEmpty(domainEvent.MerchantCustomerName)
            ? domainEvent.MerchantCustomerName
            : domainEvent.UserName;

        // If we don't have valid buyer information, skip buyer creation/verification
        if (string.IsNullOrEmpty(buyerUserId) || string.IsNullOrEmpty(buyerUserName))
        {
            _logger.LogInformation("No valid buyer information available for payment reference {PaymentReference}. Skipping buyer creation/verification.",
                domainEvent.PaymentReference);
            return;
        }

        var buyer = await _buyerRepository.FindAsync(buyerUserId);
        var buyerExisted = buyer is not null;

        if (!buyerExisted)
        {
            buyer = new Buyer(buyerUserId, buyerUserName);
            _logger.LogInformation("Creating new buyer for Interswitch payment: {BuyerId}", buyerUserId);
        }

        // Create or verify Interswitch payment method if we have payment details
        if (domainEvent.PaymentId.HasValue)
        {
            var paymentMethod = buyer!.VerifyOrAddInterSwitchPaymentMethod(
                alias: "Interswitch Payment",
                paymentReference: domainEvent.PaymentReference,
                cardHolderName: buyerUserName,
                orderId: domainEvent.OrderId);

            _logger.LogInformation("Created/verified Interswitch payment method for buyer {BuyerId} with payment reference {PaymentReference} and order {OrderId}",
                buyerUserId, domainEvent.PaymentReference, domainEvent.OrderId);
        }

        if (!buyerExisted)
        {
            _buyerRepository.Add(buyer);
        }

        await _buyerRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);

        OrderingApiTrace.LogOrderBuyerAndPaymentValidatedOrUpdated(_logger, buyer.Id, domainEvent.OrderId);
    }
}
