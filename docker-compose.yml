services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - default

  musewears.database:
    image: ankane/pgvector
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: aoOTDAvCKvzwfxYevrMtESNw9rDRp8KD
      POSTGRES_DB: musewears
    # optional: expose for host testing
    ports:
      - "5432:5432"
    networks:
      - default

  catalog.database:
    image: ankane/pgvector
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: aoOTDAvCKvzwfxYevrMtESNw9rDRp8KD
      POSTGRES_DB: catalog
    # if you need host-side access, map a different port:
    ports:
      - "5433:5432"
    networks:
      - default
      
  server:
    platform: linux/arm64
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_CONFIGURATION: Release
        ASPNETCORE_ENVIRONMENT: Production
    depends_on:
      - redis
      - musewears.database
      - catalog.database
#    image: musewears/server
    ports:
      - "5001:80"
    environment:
      # these will be substituted from the .env file or CLI
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT}
      - ConnectionStrings__DefaultConnection=${DB_CONN}
      - ConnectionStrings__CatalogConnection=${DB_CATALOG_CONN}
      - ConnectionStrings__Redis=${Redis}
    # mount your local code in dev if you like:
#    volumes:
#      - type: bind
#        source: ./Server
#        target: /app
    # any `*.sql` or `*.sh` in ./db-init will be run at container startup
    #    volumes:
    #      - ./db-init:/docker-entrypoint-initdb.d:ro
    networks:
      - default

networks:
  default:
    driver: bridge

