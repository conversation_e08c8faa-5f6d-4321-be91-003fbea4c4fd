# fly.toml app configuration file generated for musewears-little-pond-9554 on 2024-02-25T23:03:01-08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'musewears-little-pond-9554'
primary_region = 'lhr'
kill_signal = 'SIGINT'
kill_timeout = '5s'

[experimental]
  auto_rollback = true

[build]

[env]
  ASPNETCORE_ENVIRONMENT = 'Production'
  DATABASE_PATH = './var/lib/litefs/app.db'

[[mounts]]
  source = 'litefs'
  destination = '/var/lib/litefs'

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[services]]
  protocol = 'tcp'
  internal_port = 80
  processes = ['app']

[[services.ports]]
    port = 80
    handlers = ['http']
    force_https = true

[[services.ports]]
    port = 443
    handlers = ['tls', 'http']

  [services.concurrency]
    type = 'connections'
    hard_limit = 25
    soft_limit = 20

[[services.tcp_checks]]
    interval = '15s'
    timeout = '2s'
    grace_period = '1s'

[[vm]]
  size = 'shared-cpu-1x'
  cpu_kind = 'shared'
  cpus = 1
  memory_mb = 256
