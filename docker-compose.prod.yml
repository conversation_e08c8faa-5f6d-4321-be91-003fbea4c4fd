services:
  server:
#    env_file:
#      - .env.prod
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - DB_CONN=Host=Host=dpg-d1bbn2odl3ps73eh3ugg-a;Database=musewears_db_v5ez;Username=root;Password=kVaycvrJCnXEtMifgM8JtUZGTmJkZgSy
      - DB_CATALOG_CONN=postgresql://root:<EMAIL>/catalog_db_gqiv
      - Redis=red-d1bbcm2dbo4c73cc1iog:6379
    deploy:
      replicas: 2
      restart_policy:
        condition: on-failure