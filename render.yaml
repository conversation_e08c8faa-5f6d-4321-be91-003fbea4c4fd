databases:
  # Main PostgreSQL database
  - name: musewears-database
    databaseName: musewears
    user: root
    plan: free
    ipAllowList: [] # Only allow internal connections
#    ipAllowList: # allow external connections from everywhere
#      - source: 0.0.0.0/0
#        description: everywhere

  # Catalog PostgreSQL database  
#  - name: catalog-database
#    databaseName: catalog
#    user: root
#    plan: free
#    ipAllowList: [] # Only allow internal connections
##    ipAllowList: # allow external connections from everywhere
##      - source: 0.0.0.0/0
##        description: everywhere

services:
  # Redis service (Key-Value store)
  - type: keyvalue
    name: redis
    plan: free
    region: oregon
    maxmemoryPolicy: allkeys-lru
    ipAllowList: [] # Only allow internal connections
#    ipAllowList: # allow external connections from everywhere
#      - source: 0.0.0.0/0
#        description: everywhere

  # Main web application
  - type: web
    name: musewears
    plan: free
    runtime: docker
    dockerfilePath: ./Dockerfile
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: musewears-database
          property: connectionString
      - key: ConnectionStrings__CatalogConnection
        value: postgresql://root:<EMAIL>/catalog_db_gqiv
      #        fromDatabase:
#          name: catalog-database
#          property: connectionString
      - key: ConnectionStrings__Redis
        fromService:
          type: keyvalue
          name: redis
          property: connectionString
      - key: INTERSWITCH_SECRET_KEY
        sync: false # Set this manually in Render dashboard
#    healthCheckPath: /health