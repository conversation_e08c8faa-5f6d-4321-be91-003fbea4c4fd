FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
#FROM --platform=linux/amd64 mcr.microsoft.com/dotnet/aspnet:9.0 AS base
#FROM --platform=linux/amd64 mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

EXPOSE 80

# # Install EF Core tools
# RUN dotnet tool install --global dotnet-ef

# # Add the .NET Core SDK tools directory to the PATH environment variable
# ENV PATH="$PATH:/root/.dotnet/tools"

# allow choosing Debug or Release at build-time (default to Release)
ARG BUILD_CONFIGURATION=Release
ENV BUILD_CONFIGURATION=${BUILD_CONFIGURATION}

# Declare the argument
ARG INTERSWITCH_SECRET_KEY

# Set the environment variable from the argument
ENV INTERSWITCH_SECRET_KEY=$INTERSWITCH_SECRET_KEY

# CRITICAL: Set architecture environment variables to force x64 tools
#ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1
#ENV GRPC_TOOLS_OS_ARCH=linux-x64

#EXPOSE 443
COPY ./Musewears/Server/Musewears.Server.csproj .

#RUN dotnet restore Musewears/Server/Musewears.Server.csproj
COPY . .
RUN dotnet build Musewears/Server/Musewears.Server.csproj -c ${BUILD_CONFIGURATION} -o /app/build

# # Create and apply migrations
# WORKDIR /src/Musewears/Server
# RUN dotnet ef migrations add InitialMigration2
# RUN dotnet ef database update

FROM build AS publish
RUN dotnet publish Musewears/Server/Musewears.Server.csproj -c ${BUILD_CONFIGURATION} -o /app/publish

#FROM nginx:alpine AS final
#WORKDIR /usr/share/nginx/html
#COPY --from=publish /app/publish/wwwroot .
#COPY nginx.conf /etc/nginx/nginx.conf

FROM build AS final
WORKDIR /app
COPY --from=publish /app/publish .

# inject env (e.g. secrets, ASPNETCORE_ENVIRONMENT)
ARG ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT}

# ENTRYPOINT [ "dotnet", "Musewears.Server.dll" ]
CMD ASPNETCORE_URLS=http://*:$PORT dotnet Musewears.Server.dll
#RUN chmod +x ./Musewears
#CMD ["./Musewears", "--urls", "http://0.0.0.0:8080"]

# RUN apt-get update -y && apt-get install -y ca-certificates fuse3 sqlite3
# COPY --from=flyio/litefs:0.5 /usr/local/bin/litefs /usr/local/bin/litefs

# Our final Docker image stage starts here.
# FROM alpine
# ARG LITEFS_CONFIG=litefs.yml

# Copy the possible LiteFS configurations.
# ADD ./litefs.yml /tmp/litefs.yml

# Move the appropriate LiteFS config file to /etc/ (this one will be
# used by LiteFS). By default this is the config file used on Fly.io,
# but it's set appropriately to other files for the docker setup in
# docker-compose.yml
# RUN cp /tmp/$LITEFS_CONFIG /etc/litefs.yml

# Setup our environment to include FUSE & SQLite. We install ca-certificates
# so we can communicate with the Consul server over HTTPS. cURL is added so
# we can call our HTTP endpoints for debugging.
# RUN apk add bash fuse3 sqlite ca-certificates curl

# ENTRYPOINT litefs mount