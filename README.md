# Musewears - E-Commerce Fashion Platform

Musewears is a modern, full-stack e-commerce platform built with .NET 9, Blazor, and a microservices-inspired monolithic architecture. It provides a complete shopping experience for fashion items with advanced features like AI-powered search, real-time inventory management, and integrated payment processing.

## 🏗️ Architecture Overview

### Design Patterns & Architecture Style
- **Monolithic Architecture**: Single deployable unit with modular design
- **Domain-Driven Design (DDD)**: Rich domain models with aggregates and domain events
- **CQRS Pattern**: Command Query Responsibility Segregation for complex operations
- **Repository Pattern**: Data access abstraction
- **Unit of Work Pattern**: Transaction management
- **Event-Driven Architecture**: Domain events for loose coupling

### Technology Stack
- **Frontend**: Blazor WebAssembly + Blazor Server (Hybrid)
- **Backend**: ASP.NET Core 9 Web API
- **Database**: PostgreSQL with Entity Framework Core
- **Caching**: Redis for basket/session management
- **Communication**: gRPC for internal services, HTTP APIs for external
- **AI/ML**: Vector embeddings for semantic search (pgvector)
- **Payment**: Interswitch payment gateway integration
- **Deployment**: Docker, Aspire for orchestration

## 📁 Project Structure

```
Musewears/
├── AppHost/                    # .NET Aspire orchestration host
├── Server/                     # Main Blazor Server application
├── Client/                     # Blazor WebAssembly client
├── Admin/                      # Admin dashboard (Blazor Server)
├── Catalog.API/               # Product catalog microservice
├── Basket.API/                # Shopping cart microservice (gRPC)
├── Ordering/                  # Order management domain
│   ├── Ordering.API/          # Order API and application layer
│   ├── Ordering.Domain/       # Domain models and business logic
│   ├── Ordering.Infrastructure/ # Data access and external services
│   └── Ordering.UnitTests/    # Domain and application tests
├── WebAppComponents/          # Shared Blazor components
├── Shared/                    # Common models and utilities
├── ServiceDefaults/           # Common service configurations
├── EventBus/                  # Event bus abstractions
├── EventBusRabbitMQ/         # RabbitMQ implementation
├── DomainEventBus/           # Domain event handling
├── DomainEventLogEF/         # Domain event persistence
└── Server.UnitTests/         # Integration and webhook tests
```

## 🎯 Core Features

### For Customers
- **Product Browsing**: Browse fashion items by category, brand, and type
- **AI-Powered Search**: Semantic search using vector embeddings
- **Product Variants**: Color and size selection with inventory tracking
- **Shopping Cart**: Real-time cart management with Redis persistence
- **User Authentication**: Identity-based authentication and authorization
- **Secure Checkout**: Interswitch payment gateway integration
- **Order Tracking**: Real-time order status updates
- **Product Reviews**: Rating and review system with aggregated scores

### For Vendors
- **Product Management**: Create, update, and manage product listings
- **Inventory Control**: Stock level management with reorder thresholds
- **Analytics Dashboard**: Sales and inventory analytics
- **Order Management**: View and process customer orders

### For Administrators
- **Catalog Management**: Full CRUD operations on products, brands, and categories
- **User Management**: Customer and vendor account administration
- **Order Oversight**: Monitor all orders and payment statuses
- **System Analytics**: Platform-wide metrics and reporting

## 🔄 How the Shopping App Works

### Customer Journey

1. **Product Discovery**
   - Browse homepage with featured products
   - Navigate to shop page for full catalog
   - Use search functionality (text or AI-powered semantic search)
   - Filter by categories, brands, price ranges

2. **Product Selection**
   - View detailed product pages with images, descriptions, and reviews
   - Select color and size variants
   - Check availability and pricing
   - Add items to shopping cart

3. **Shopping Cart Management**
   - View cart contents with real-time updates
   - Modify quantities or remove items
   - Cart persists across sessions using Redis

4. **Checkout Process**
   - User authentication required
   - Enter shipping address information
   - Review order summary and total
   - Proceed to payment

5. **Payment Processing**
   - Integration with Interswitch payment gateway
   - Secure payment form with multiple payment methods
   - Real-time payment status updates via webhooks
   - Order confirmation and receipt generation

6. **Order Fulfillment**
   - Order status tracking (Submitted → Paid → Shipped → Delivered)
   - Email notifications for status changes
   - Order history and details accessible in user account

### Vendor Workflow

1. **Product Management**
   - Access admin dashboard
   - Create new product listings with details, images, and variants
   - Set pricing, inventory levels, and reorder thresholds
   - Manage product categories and brands

2. **Inventory Management**
   - Monitor stock levels in real-time
   - Receive low-stock alerts
   - Update inventory quantities
   - Track product performance metrics

3. **Order Processing**
   - View incoming orders
   - Update order status as items are processed
   - Manage shipping and fulfillment

## 🚀 Getting Started

### Prerequisites
- .NET 9 SDK
- Docker and Docker Compose
- PostgreSQL (or use Docker)
- Redis (or use Docker)

### Development Setup

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd Musewears
   ```

2. **Using .NET Aspire (Recommended for Development)**
   ```bash
   cd Musewears/AppHost
   dotnet run
   ```
   This starts all services with proper orchestration and monitoring.

3. **Manual Setup with Docker Compose**
   ```bash
   # Development environment
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

   # Production environment
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up
   ```

4. **Local Development (Individual Services)**
   ```bash
   # Start dependencies
   docker-compose up redis musewears.database catalog.database

   # Run the main server
   cd Musewears/Server
   dotnet run
   ```

### Environment Configuration

Create environment variables or update `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=musewears;Username=root;Password=yourpassword",
    "CatalogConnection": "Host=localhost;Database=catalog;Username=root;Password=yourpassword",
    "Redis": "localhost:6379"
  },
  "INTERSWITCH_SECRET_KEY": "your-interswitch-secret-key"
}
```

## 🧪 Testing

### Running All Tests
```bash
# Run all tests
./run-tests.sh          # Linux/macOS
run-tests.bat           # Windows

# Or using dotnet CLI
dotnet test
```

### Running Interswitch Integration Tests
```bash
# Run Interswitch-specific tests
./run-interswitch-tests.sh    # Linux/macOS
run-interswitch-tests.bat     # Windows
```

### Test Categories

1. **Unit Tests** (`Server.UnitTests/`)
   - Webhook signature verification
   - Payment processing logic
   - Order status management
   - Buyer association functionality

2. **Domain Tests** (`Ordering.UnitTests/`)
   - Domain model validation
   - Business rule enforcement
   - Domain event handling
   - Aggregate behavior

3. **Integration Tests**
   - End-to-end payment flows
   - Database operations
   - External service integration

## 🚢 Deployment

### Docker Deployment
```bash
# Build and deploy
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Cloud Deployment

The application supports deployment to:
- **Fly.io** (configured with `fly.toml`)
- **Render** (configured with `render.yaml`)
- **Azure** (configured with `azure-pipelines.yml`)

### Production Checklist
- [ ] Set production connection strings
- [ ] Configure Interswitch production credentials
- [ ] Set up SSL certificates
- [ ] Configure monitoring and logging
- [ ] Set up backup strategies
- [ ] Configure CDN for static assets

## 🔧 Key Design Decisions

### Monolithic vs Microservices
- **Choice**: Monolithic architecture with modular design
- **Rationale**: Simpler deployment, easier debugging, reduced complexity
- **Future**: Can be split into microservices as needed

### Domain Events vs Integration Events
- **Choice**: Domain events within the monolith
- **Rationale**: Maintains consistency, simpler transaction management
- **Implementation**: MediatR for in-process event handling

### Payment Integration
- **Choice**: Interswitch webhook-based integration
- **Features**: HMAC signature verification, idempotent processing
- **Security**: Secure webhook validation and payment status tracking

### Data Storage Strategy
- **Primary Database**: PostgreSQL for ACID compliance
- **Caching**: Redis for session and cart data
- **Search**: Vector embeddings for AI-powered product search

## 📊 Monitoring and Observability

- **Aspire Dashboard**: Development-time monitoring and debugging
- **Structured Logging**: Comprehensive logging throughout the application
- **Health Checks**: Built-in health monitoring for all services
- **Metrics**: Performance and business metrics collection

## 🔐 Security Features

- **Authentication**: ASP.NET Core Identity with JWT tokens
- **Authorization**: Role-based access control (Customer, Vendor, Admin)
- **Payment Security**: HMAC-SHA512 webhook signature verification
- **Data Protection**: Encrypted sensitive data storage
- **CORS**: Configured for secure cross-origin requests

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 🏛️ Domain Model Deep Dive

### Order Aggregate
The Order aggregate is the core of the ordering domain:

<augment_code_snippet path="Musewears/Ordering/Ordering.Domain/AggregatesModel/OrderAggregate/Order.cs" mode="EXCERPT">
````csharp
public class Order : Entity, IAggregateRoot
{
    public DateTime OrderDate { get; private set; }
    public Address Address { get; private set; }
    public int? BuyerId { get; private set; }
    public OrderStatus OrderStatus { get; private set; }

    public void ProcessInterSwitchPayment(string paymentReference, string userId, string userName, string paymentStatus, int? paymentId = null, string? merchantCustomerId = null, string? merchantCustomerName = null)
    {
        // Raises domain events for payment processing
        AddDomainEvent(new InterswitchPaymentProcessedDomainEvent(...));
    }
}
````
</augment_code_snippet>

### Transaction Aggregate
Handles Interswitch webhook events with dedicated domain events:

<augment_code_snippet path="Musewears/Ordering/Ordering.Domain/AggregatesModel/TransactionAggregate/Transaction.cs" mode="EXCERPT">
````csharp
public void ProcessTransactionCreated()
{
    Status = TransactionStatus.Created;
    UpdatedAt = DateTime.UtcNow;
    AddDomainEvent(new TransactionCreatedDomainEvent(this));
}
````
</augment_code_snippet>

### Buyer Aggregate
Manages customer information and payment methods:

<augment_code_snippet path="Musewears/Ordering/Ordering.Domain/AggregatesModel/BuyerAggregate/Buyer.cs" mode="EXCERPT">
````csharp
public PaymentMethod VerifyOrAddInterSwitchPaymentMethod(
    string alias, string paymentReference, string cardHolderName, int orderId)
{
    // Creates Interswitch-specific payment methods
    var payment = new PaymentMethod(alias, paymentReference, cardHolderName);
    _paymentMethods.Add(payment);
    AddDomainEvent(new BuyerAndPaymentMethodVerifiedDomainEvent(this, payment, orderId));
    return payment;
}
````
</augment_code_snippet>

## 🔄 Event-Driven Architecture

### Domain Events Flow
1. **Order Creation** → `OrderStartedDomainEvent` → Creates/Associates Buyer
2. **Payment Webhook** → `TransactionCreatedDomainEvent` → Associates Transaction with Order
3. **Payment Success** → `OrderStatusChangedToPaidDomainEvent` → Updates Order Status

### Event Handlers
Domain event handlers implement business logic:

<augment_code_snippet path="Musewears/Ordering/Ordering.API/Application/DomainEventHandlers/TransactionCreatedDomainEventHandler.cs" mode="EXCERPT">
````csharp
public async Task Handle(TransactionCreatedDomainEvent domainEvent, CancellationToken cancellationToken)
{
    var transaction = domainEvent.Transaction;

    // Find order by merchant reference
    var order = await _orderRepository.GetByPaymentReferenceAsync(transaction.MerchantReference);

    if (order is not null)
    {
        // Associate transaction with order
        transaction.AssociateWithOrder(order.Id);

        // Create or update buyer if customer info available
        await CreateOrUpdateBuyerAsync(transaction, order);
    }
}
````
</augment_code_snippet>

## 🛒 Shopping Cart Implementation

### gRPC Basket Service
The basket uses gRPC for high-performance communication:

<augment_code_snippet path="Musewears/Basket.API/Grpc/BasketService.cs" mode="EXCERPT">
````csharp
public override async Task<CustomerBasketResponse> GetBasket(GetBasketRequest request, ServerCallContext context)
{
    var userId = GetCurrentUserId();
    if (string.IsNullOrEmpty(userId))
    {
        return new CustomerBasketResponse();
    }

    var data = await repository.GetBasketAsync(userId);
    return data is not null ? MapToCustomerBasketResponse(data) : new();
}
````
</augment_code_snippet>

### Redis Persistence
Shopping carts are persisted in Redis for performance and scalability.

## 🔍 AI-Powered Search

### Vector Embeddings
Products support semantic search using vector embeddings:

<augment_code_snippet path="Musewears/Catalog.API/Services/CatalogAI.cs" mode="EXCERPT">
````csharp
public async ValueTask<Vector?> GetEmbeddingAsync(CatalogItem item)
{
    if (!IsEnabled) return null;

    var embedding = await embeddingGenerator.GenerateVectorAsync(CatalogItemToString(item));
    return new Vector(embedding[0..EmbeddingDimensions]);
}
````
</augment_code_snippet>

### Semantic Search API
Search by meaning, not just keywords:

<augment_code_snippet path="Musewears/Catalog.API/Apis/CatalogApi.cs" mode="EXCERPT">
````csharp
public static async Task<Results<Ok<PaginatedItems<CatalogItem>>, RedirectToRouteHttpResult>> GetItemsBySemanticRelevance(
    [AsParameters] PaginationRequest paginationRequest,
    [AsParameters] CatalogServices services,
    [Description("The text string to use when search for related items in the catalog"), Required, MinLength(1)] string text)
{
    if (!services.CatalogAi.IsEnabled)
    {
        return await GetItemsByName(paginationRequest, services, text);
    }

    var vector = await services.CatalogAi.GetEmbeddingAsync(text);
    var itemsOnPage = await services.Context.CatalogItems
        .OrderBy(c => c.Embedding.CosineDistance(vector))
        .Skip(pageSize * pageIndex)
        .Take(pageSize)
        .ToListAsync();
}
````
</augment_code_snippet>

## 💳 Payment Integration Details

### Interswitch Webhook Security
All webhooks are secured with HMAC-SHA512 signature verification:

<augment_code_snippet path="Musewears/Server/Controllers/InterswitchWebhookController.cs" mode="EXCERPT">
````csharp
[HttpPost("interswitch-notification")]
public async Task<IActionResult> HandleInterswitchNotification()
{
    var signature = Request.Headers["X-Interswitch-Signature"].FirstOrDefault();
    if (string.IsNullOrEmpty(signature))
    {
        return BadRequest("Missing X-Interswitch-Signature header.");
    }

    var requestBody = await ReadRequestBodyAsync();
    if (!VerifySignature(requestBody, signature))
    {
        return Unauthorized();
    }

    // Process webhook events...
}
````
</augment_code_snippet>

### Payment Flow
1. **Checkout** → Generate payment reference → Create order
2. **Payment Gateway** → Customer pays → Webhook notification
3. **Webhook Processing** → Verify signature → Update order status
4. **Order Completion** → Send confirmation → Update inventory

## 🧪 Testing Strategy

### Comprehensive Test Coverage
- **Unit Tests**: Domain logic and business rules
- **Integration Tests**: API endpoints and database operations
- **Webhook Tests**: Payment processing and signature verification
- **End-to-End Tests**: Complete user workflows

### Test Execution
```bash
# Run all tests with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test categories
dotnet test --filter Category=Integration
dotnet test --filter Category=Webhook

# Run Interswitch-specific tests
./Musewears/run-interswitch-tests.sh    # Linux/macOS
./Musewears/run-interswitch-tests.bat   # Windows
```

### Test Structure
Each test project follows the same pattern:
- **Arrange**: Set up test data and mocks
- **Act**: Execute the operation being tested
- **Assert**: Verify the expected outcome

<augment_code_snippet path="Musewears/Server.UnitTests/Controllers/InterswitchWebhookControllerTests.cs" mode="EXCERPT">
````csharp
[Fact]
public async Task HandleInterswitchNotification_Should_Process_Transaction_Created_Event()
{
    // Arrange
    var secretKey = "test-secret-key";
    Environment.SetEnvironmentVariable("INTERSWITCH_SECRET_KEY", secretKey);

    var transactionEvent = new TransactionEvent
    {
        Event = "TRANSACTION.CREATED",
        Data = new TransactionEventData
        {
            PaymentReference = "PAY123456",
            PaymentId = 789,
            Amount = 100.00m,
            ResponseCode = "00"
        }
    };

    // Act & Assert...
}
````
</augment_code_snippet>

## 🚀 Performance Optimizations

### Database Optimizations
- **Indexes**: Optimized queries with proper indexing
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: LINQ queries optimized for performance
- **Pagination**: Large datasets handled with efficient pagination

### Caching Strategy
- **Redis**: Session data and shopping carts
- **In-Memory**: Frequently accessed configuration data
- **CDN**: Static assets and images
- **Query Caching**: Database query result caching

### Async/Await Patterns
All I/O operations use async/await for better scalability and responsiveness.

## 📊 Business Metrics & Analytics

### Key Performance Indicators
- Order conversion rates
- Average order value
- Product popularity metrics
- Customer retention rates
- Payment success rates
- Inventory turnover

### Analytics Dashboard
Vendors and administrators can access real-time analytics:

<augment_code_snippet path="Musewears/Catalog.API/Controllers/CatalogController.cs" mode="EXCERPT">
````csharp
[HttpGet("analytics")]
public async Task<ActionResult<CatalogAnalytics>> GetAnalytics()
{
    var totalItems = await catalogServices.Context.CatalogItems.CountAsync();
    var totalBrands = await catalogServices.Context.CatalogBrands.CountAsync();
    var totalTypes = await catalogServices.Context.CatalogTypes.CountAsync();
    var lowStockItems = await catalogServices.Context.CatalogItems
        .Where(i => i.AvailableStock <= i.RestockThreshold)
        .CountAsync();

    return Ok(new CatalogAnalytics
    {
        TotalItems = totalItems,
        TotalBrands = totalBrands,
        TotalTypes = totalTypes,
        LowStockItems = lowStockItems
    });
}
````
</augment_code_snippet>

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check PostgreSQL connection
   docker-compose logs musewears.database

   # Test connection manually
   psql -h localhost -U root -d musewears
   ```

2. **Redis Connection Issues**
   ```bash
   # Check Redis status
   docker-compose logs redis

   # Test Redis connection
   redis-cli ping
   ```

3. **Payment Webhook Issues**
   ```bash
   # Check webhook logs
   docker-compose logs server | grep "Interswitch"

   # Verify webhook signature
   echo "Check INTERSWITCH_SECRET_KEY environment variable"
   ```

4. **gRPC Communication Issues**
   ```bash
   # Check gRPC service health
   grpcurl -plaintext localhost:5099 grpc.health.v1.Health/Check
   ```

### Debug Mode
Enable detailed logging in `appsettings.Development.json`:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Warning",
      "Grpc": "Debug"
    }
  }
}
```

### Health Checks
The application includes comprehensive health checks:
- Database connectivity
- Redis availability
- External service dependencies
- Memory and disk usage

Access health checks at: `https://localhost:7135/health`

## 🔐 Security Best Practices

### Authentication & Authorization
- **JWT Tokens**: Secure token-based authentication
- **Role-Based Access**: Customer, Vendor, Admin roles
- **API Protection**: All sensitive endpoints require authentication

### Data Protection
- **Encryption**: Sensitive data encrypted at rest
- **HTTPS**: All communication encrypted in transit
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection Prevention**: Parameterized queries and EF Core

### Payment Security
- **PCI Compliance**: No card data stored locally
- **Webhook Verification**: HMAC-SHA512 signature validation
- **Secure Redirects**: Validated redirect URLs only

## 🌐 Internationalization & Localization

### Multi-Currency Support
- **Currency Handling**: Decimal precision for financial calculations
- **Exchange Rates**: Support for multiple currencies
- **Regional Pricing**: Location-based pricing strategies

### Localization Ready
- **Resource Files**: Prepared for multiple languages
- **Date/Time Formatting**: Culture-specific formatting
- **Number Formatting**: Regional number and currency formats

## 📱 Mobile Responsiveness

### Responsive Design
- **Bootstrap Integration**: Mobile-first responsive design
- **Touch-Friendly**: Optimized for touch interactions
- **Progressive Web App**: PWA capabilities for mobile experience

### Performance on Mobile
- **Lazy Loading**: Images and components loaded on demand
- **Minimal Bundle Size**: Optimized JavaScript bundles
- **Offline Support**: Basic offline functionality

## 🔄 CI/CD Pipeline

### Azure DevOps Integration
The project includes Azure Pipelines configuration:

```yaml
# azure-pipelines.yml
trigger:
- main

pool:
  vmImage: 'ubuntu-latest'

steps:
- task: DotNetCoreCLI@2
  displayName: 'Restore packages'
  inputs:
    command: 'restore'
    projects: '**/*.csproj'

- task: DotNetCoreCLI@2
  displayName: 'Build solution'
  inputs:
    command: 'build'
    projects: '**/*.csproj'

- task: DotNetCoreCLI@2
  displayName: 'Run tests'
  inputs:
    command: 'test'
    projects: '**/*Tests.csproj'
```

### Deployment Strategies
- **Blue-Green Deployment**: Zero-downtime deployments
- **Feature Flags**: Gradual feature rollouts
- **Database Migrations**: Automated schema updates

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

## 📚 Additional Resources

- **Domain-Driven Design**: [DDD Reference](https://domainlanguage.com/ddd/)
- **Blazor Documentation**: [Microsoft Blazor Docs](https://docs.microsoft.com/aspnet/core/blazor/)
- **Interswitch API**: [Payment Integration Guide](https://developer.interswitch.com/)
- **PostgreSQL**: [Database Documentation](https://www.postgresql.org/docs/)
- **Redis**: [Caching Documentation](https://redis.io/documentation)

For detailed technical documentation, see the individual project README files and inline code documentation.
