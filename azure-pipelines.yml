# Starter pipeline

# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
  - master

resources:
  - repo: self

variables:
  tag: "$(Build.BuildId)"

# pool:
#   vmImage: ubuntu-latest

# steps:
# - task: PowerShell@2
#   inputs:
#     targetType: 'inline'
#     script: |
#       git checkout $(Build.SourceBranchName)
#       git remote add heroku https://heroku:$(pat)@git.heroku.com/musewears.git
#       git push heroku $(Build.SourceBranchName)

stages:
  - stage: Build
    displayName: Build image
    jobs:
      - job: Build
        displayName: Build & Push Docker image
        pool:
          vmImage: ubuntu-latest

        steps:
          - task: Docker@2
            displayName: Build docker image
            inputs:
              containerRegistry: "dockerhub_connection"
              repository: "francispace/musewears_server"
              command: "build"
              Dockerfile: "Dockerfile"
              tags: |
                latest
              arguments: --platform linux/amd64 --build-arg INTERSWITCH_SECRET_KEY=$(INTERSWITCH_SECRET_KEY)
          - task: Docker@2
            displayName: Push docker image
            inputs:
              containerRegistry: "dockerhub_connection"
              repository: "francispace/musewears_server"
              command: "push"
              tags: |
                latest

  # - stage: Build
  #   displayName: Build & Deploy with Docker Compose
  #   jobs:
  #     - job: Deploy
  #       displayName: Build & Deploy to Docker Hub
  #       pool:
  #         vmImage: ubuntu-latest
  #       steps:
  #         - task: DockerCompose@0
  #           displayName: Build with Docker Compose
  #           inputs:
  #             containerregistrytype: "Container Registry"
  #             # dockerRegistryEndpoint: "dockerhub_connection"
  #             dockerComposeFile: "docker-compose.yml"
  #             projectName: "francispace/musewears_server"
  #             action: "Build services" # This action builds the images using Docker Compose.
  #             additionalImageTags: "latest"
  #             # arguments: --platform linux/amd64
  #         - task: DockerCompose@0
  #           displayName: Push with Docker Compose
  #           inputs:
  #             containerregistrytype: "Container Registry"
  #             dockerRegistryEndpoint: "dockerhub_connection"
  #             dockerComposeFile: "docker-compose.yml"
  #             projectName: "francispace/musewears_server"
  #             action: "Push services" # This action pushes the built images to the registry.
  #             additionalImageTags: "latest"

  # - task: DockerCompose@0
  #   inputs:
  #     action: "Build services"
  #     dockerComposeFile: "docker-compose.yml"
  #     projectName: "musewears_image"
  #     # additionalImageTags: "linux/amd64" # Specify the platform tag here
  # - script: |
  #     echo "$(DOCKERHUB_PASSWORD)" | docker login --username "$(DOCKERHUB_USERNAME)" --password-stdin
  #     docker-compose push
  #   displayName: "Push Docker images to Docker Hub"

  # - stage: Deploy
  #   displayName: Build & Deploy with Docker Compose
  #   jobs:
  #     - job: Deploy
  #       displayName: Deploy with Docker Compose
  #       pool:
  #         vmImage: ubuntu-latest
  #       steps:
  #         - task: DockerCompose@0
  #           inputs:
  #             action: "Build services"
  #             dockerComposeFile: "docker-compose.yml"
  #             projectName: "musewears_image"
  #             # additionalImageTags: "linux/amd64" # Specify the platform tag here
  #         - script: |
  #             echo "$(DOCKERHUB_PASSWORD)" | docker login --username "$(DOCKERHUB_USERNAME)" --password-stdin
  #             docker-compose push
  #           displayName: "Push Docker images to Docker Hub"

  - stage: Deploy
    displayName: Trigger render.com image deployment
    dependsOn: Build
    condition: succeeded()
    jobs:
      - job: Deploy
        displayName: Deploy
        pool:
          vmImage: ubuntu-latest
        steps:
          - task: PowerShell@2
            inputs:
              targetType: "inline"
              script: |
                $imageUrl = "francispace/musewears_server:latest"
                
                Invoke-RestMethod -Uri $(RENDER_DEPLOY_WEBHOOK_URL) -Method Post
                Write-Host "Deployment triggered successfully with image: $imageUrl"

  # - task: Docker@2
  #   displayName: Build docker image
  #   inputs:
  #     containerRegistry: "dockerhub_connection" # Specify the Docker Hub service connection
  #     repository: "francispace/musewears_image" # Docker Hub repository name
  #     command: "buildAndPush"
  #     Dockerfile: 'Dockerfile' # Path to your Dockerfile
  #     arguments: '--platform linux/amd64'
  #     # platform: "linux/amd64" # Specify the platform for the Docker image
  #     tags: |
  #       latest
  # - script: |
  #     docker buildx build --platform linux/amd64 -t francispace/musewears_image:latest .
  #     docker push francispace/musewears_image:latest
  # steps:
  #   # - task: Docker@2
  #   #   displayName: Build an image
  #   #   inputs:
  #   #     command: build
  #   #     dockerfile: '$(Build.SourcesDirectory)/Dockerfile'
  #   # useDefaultContext: false
  #   # buildContext: '**'
  #   # - task: CopyFiles@2
  #   #   inputs:
  #   #     SourceFolder: '$(System.DefaultWorkingDirectory)'
  #   #     TargetFolder: '$(Build.ArtifactStagingDirectory)'
  #   - script: |
  #       ls
  #       # npm install -g render-cli
  #       # render login --token rnd_6XWiQ4oyjhXbtgrzmDsY0ISJEE89
  #       # render deploy --service <your-service-id> --type web --image <your-docker-repository>:$(tag)
  #       # curl -L https://fly.io/install.sh | sh
  #       # export FLYCTL_INSTALL="/home/<USER>/.fly"
  #       # export PATH="$FLYCTL_INSTALL/bin:$PATH"
  #       # flyctl deploy -a, --app musewears -t, --access-token $(FLY_API_TOKEN)
  #     displayName: "Install fly.io cli and Deploy"
  #     env:
  #       FLY_API_TOKEN: $(FLY_API_TOKEN)

# steps:
# - task: Docker@2
#    displayName: 'Build an image'
#    inputs:
#      command: build
#      Dockerfile: '$(Build.SourcesDirectory)/Dockerfile'
# - script: |
#     # cd ./Musewears/Server
#     # npm install -global heroku
#     # sudo snap install --classic heroku
#     # sudo snap install heroku --classic
#     # curl https://cli-assets.heroku.com/install-ubuntu.sh | sh
#     npm install -g netlify-cli
#   displayName: 'Netlify cli'
# - script: |
#     # echo CONTAINER LOGIN
#     # heroku container:login
#     # echo PUSH
#     # heroku container:push -a $HEROKU_APP_NAME web
#     # echo RELEASE
#     # heroku container:release -a $HEROKU_APP_NAME web

#     netlify deploy --prod --dir '$(Build.SourcesDirectory)/app/publish'
#   displayName: 'Netlify Deploy'
#   env:
#     NETLIFY_SITE_ID: $(site_id)
#     NETLIFY_AUTH_TOKEN: $(NETLIFY_PAT)
# displayName: 'heroku push'
# env:
#   HEROKU_API_KEY: $(HEROKU_API_KEY)
#   HEROKU_APP_NAME: $(HEROKU_APP_NAME)
