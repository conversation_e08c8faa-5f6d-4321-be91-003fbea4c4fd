services:
  server:
    # automatically used with `-f docker-compose.dev.yml`
    build:
      args:
        BUILD_CONFIGURATION: Debug
        ASPNETCORE_ENVIRONMENT: Development
#    env_file:
#      - .env.dev
    environment:
      # override base
      - ASPNETCORE_ENVIRONMENT=Development
      - DB_CONN=Host=musewears.database;port=5432;Database=musewears;Username=root;Password=aoOTDAvCKvzwfxYevrMtESNw9rDRp8KD
      - DB_CATALOG_CONN=Host=catalog.database;port=5432;Database=catalog;Username=root;Password=aoOTDAvCKvzwfxYevrMtESNw9rDRp8KD
      - Redis=redis:6379
    volumes:
      - ./:/src
    working_dir: /src/Musewears/Server
    command: dotnet watch --project Musewears.Server.csproj --urls http://*:80
